#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UFN服务器
"""

import logging
import threading
import time
from ufn_protocol.server import UFNServer



logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_ufn_server():
    """测试UFN服务器"""
    try:
        # 创建UFN服务器，连接到gRPC服务器
        server = UFNServer(
            host='0.0.0.0', 
            port=8080, 
            grpc_server_url='localhost:50051'
        )
        
        logger.info("UFN服务器正在启动...")
        server.start()
        
    except Exception as e:
        logger.error(f"UFN服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_ufn_server()
