#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备消息测试脚本
使用提供的设备报文测试完整的OTA升级流程
"""

import socket
import struct
import time
import logging
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DeviceSimulator:
    """设备模拟器"""
    
    def __init__(self, server_host='localhost', server_port=8080):
        self.server_host = server_host
        self.server_port = server_port
        self.socket = None
    
    def connect(self) -> bool:
        """连接到服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.server_host, self.server_port))
            logger.info(f"Connected to server {self.server_host}:{self.server_port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to server: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
            logger.info("Disconnected from server")
    
    def send_raw_message(self, hex_data: str) -> Optional[bytes]:
        """发送原始十六进制消息"""
        try:
            # 将十六进制字符串转换为字节
            data = bytes.fromhex(hex_data.replace(' ', ''))
            logger.info(f"Sending message: {hex_data}")
            logger.info(f"Message length: {len(data)} bytes")
            
            # 发送数据
            self.socket.send(data)
            
            # 接收响应
            response = self.socket.recv(1024)
            logger.info(f"Received response: {response.hex()}")
            logger.info(f"Response length: {len(response)} bytes")
            
            return response
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return None
    
    def parse_version_response(self, response: bytes):
        """解析版本响应消息"""
        if len(response) < 4:
            logger.error("Response too short")
            return
        
        try:
            length = struct.unpack('>H', response[0:2])[0]
            func_code = struct.unpack('>H', response[2:4])[0]
            
            logger.info(f"Response length: {length}")
            logger.info(f"Function code: 0x{func_code:04X}")
            
            if func_code == 0x8302:  # GET_VERSION_RESP
                if len(response) >= 12:  # 至少需要12字节来读取基本信息
                    platform_version = struct.unpack('>H', response[4:6])[0]
                    upgrade_type = response[6]
                    upgrade_control = response[7]
                    area = response[8] if len(response) > 8 else 0
                    option = response[9] if len(response) > 9 else 0
                    
                    logger.info(f"Platform version: {platform_version}")
                    logger.info(f"Upgrade type: {upgrade_type}")
                    logger.info(f"Upgrade control: 0x{upgrade_control:02X}")
                    logger.info(f"Area: {area}, Option: {option}")

                    upgrade_available = upgrade_control in [0xA1, 0xA2, 0xA3]
                    logger.info(f"Upgrade available: {upgrade_available}")

                    return {
                        'platform_version': platform_version,
                        'upgrade_type': upgrade_type,
                        'upgrade_control': upgrade_control,
                        'upgrade_available': upgrade_available,
                        'area': area,
                        'option': option
                    }
        except Exception as e:
            logger.error(f"Failed to parse version response: {e}")
        
        return None
    
    def parse_program_info_response(self, response: bytes):
        """解析程序信息响应消息"""
        if len(response) < 4:
            logger.error("Response too short")
            return
        
        try:
            length = struct.unpack('>H', response[0:2])[0]
            func_code = struct.unpack('>H', response[2:4])[0]
            
            logger.info(f"Response length: {length}")
            logger.info(f"Function code: 0x{func_code:04X}")
            
            if func_code == 0x8404:  # GET_PROGRAM_INFO_RESP
                logger.info("Received program info response")
                if len(response) >= 68:  # 4 + 64字节程序信息
                    program_info = response[4:68]
                    
                    # 解析程序信息
                    version = struct.unpack('<H', program_info[0:2])[0]
                    year, month, day, hour, minute, second = struct.unpack('6B', program_info[2:8])
                    program_length = struct.unpack('<I', program_info[16:20])[0]
                    program_sum = struct.unpack('<I', program_info[20:24])[0]
                    proc_id = program_info[24:32].decode('ascii', errors='ignore').strip('\x00')
                    
                    logger.info(f"Program version: {version}")
                    logger.info(f"Date: 20{year:02d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}")
                    logger.info(f"Program length: {program_length} bytes")
                    logger.info(f"Program checksum: 0x{program_sum:08X}")
                    logger.info(f"Program ID: {proc_id}")
                    
                    return {
                        'version': version,
                        'program_length': program_length,
                        'program_sum': program_sum,
                        'proc_id': proc_id
                    }
        except Exception as e:
            logger.error(f"Failed to parse program info response: {e}")
        
        return None


def test_ota_flow():
    """测试完整的OTA升级流程"""
    logger.info("Starting OTA flow test...")
    
    # 创建设备模拟器
    device = DeviceSimulator()
    
    if not device.connect():
        return False
    
    try:
        # 1. 发送获取版本信息请求 (8301) - 使用修正后的校验和
        logger.info("\n=== Step 1: Send Get Version Request (8301) ===")
        version_request = "00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
        
        version_response = device.send_raw_message(version_request)
        if version_response:
            version_info = device.parse_version_response(version_response)
            if version_info and version_info.get('upgrade_available', False):
                logger.info("Version check passed, upgrade available")
            else:
                logger.warning("No upgrade available or upgrade forbidden")
                return False
        else:
            logger.error("Failed to get version response")
            return False
        
        # 2. 发送获取程序信息请求 (8403) - 使用修正后的校验和
        logger.info("\n=== Step 2: Send Get Program Info Request (8403) ===")
        program_info_request = "00 08 84 03 00 00 00 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
        
        program_response = device.send_raw_message(program_info_request)
        if program_response:
            program_info = device.parse_program_info_response(program_response)
            if program_info:
                logger.info("Program info received successfully")
                
                # 3. 模拟下载数据请求 (可选)
                logger.info("\n=== Step 3: Simulate Download Data Request (Optional) ===")
                # 这里可以添加下载数据的测试
                logger.info("Download simulation skipped for this test")
                
                logger.info("\n=== OTA Flow Test Completed Successfully ===")
                return True
            else:
                logger.error("Failed to parse program info")
                return False
        else:
            logger.error("Failed to get program info response")
            return False
    
    finally:
        device.disconnect()


def main():
    """主函数"""
    logger.info("Device Message Test Script")
    logger.info("=" * 50)
    
    # 测试OTA流程
    success = test_ota_flow()
    
    if success:
        logger.info("✓ All tests passed!")
        return 0
    else:
        logger.error("✗ Some tests failed!")
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(main())
