syntax = "proto3";

package ota;

// OTA升级服务
service OTAService {
    // 查询升级包信息
    rpc QueryUpgradeInfo(UpgradeInfoRequest) returns (UpgradeInfoResponse);
    
    // 下载文件内容
    rpc DownloadFileContent(FileDownloadRequest) returns (FileDownloadResponse);
    
    // 查询文件大小
    rpc QueryFileSize(FileSizeRequest) returns (FileSizeResponse);
}

// 升级信息请求
message UpgradeInfoRequest {
    string device_id = 1;       // 设备编码
    string program_id = 2;      // 升级程序类型 (NTB2GV31, RNBMSV20)
}

// 升级信息响应
message UpgradeInfoResponse {
    string device_id = 1;       // 设备编码
    string file_url = 2;        // 文件地址
    string program_id = 3;      // 升级程序类型
    string platform_version = 4; // 平台版本
    bool available = 5;         // 是否有可用升级
}

// 文件下载请求
message FileDownloadRequest {
    string file_url = 1;        // 文件地址
}

// 文件下载响应
message FileDownloadResponse {
    bytes file_data = 1;        // 文件二进制数据
    bool success = 2;           // 下载是否成功
    string error_message = 3;   // 错误信息（如果有）
}

// 文件大小请求
message FileSizeRequest {
    string file_url = 1;        // 文件地址
}

// 文件大小响应
message FileSizeResponse {
    int64 file_size = 1;        // 文件大小（字节）
    bool success = 2;           // 查询是否成功
    string error_message = 3;   // 错误信息（如果有）
}
